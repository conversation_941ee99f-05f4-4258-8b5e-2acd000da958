'''
数据加载器模块
实现自定义的数据加载器，用于处理Prompt-based模型的输入数据

该模块提供了两个数据加载器类，用于将原始数据集转换为适用于Prompt模型的格式：
1. SinglePathPromptDataLoader: 单路径Prompt数据加载器，支持多GPU训练
2. MyPromptDataLoader: 标准Prompt数据加载器

主要功能包括：
- 将原始数据包装成Prompt格式
- 使用Tokenizer对包装后的数据进行分词和编码
- 构建适用于PyTorch的数据加载器
'''

import torch
import torch.nn as nn

from torch.utils.data.sampler import RandomSampler, SequentialSampler

from torch.utils.data import Dataset
from typing import *
from torch.utils.data._utils.collate import default_collate
from tqdm.std import tqdm
from transformers.tokenization_utils import PreTrainedTokenizer
from collections import defaultdict

from openprompt.data_utils import InputExample, InputFeatures
from openprompt.plms.utils import TokenizerWrapper
from openprompt.prompt_base import Template, Verbalizer
from openprompt.utils import signature


from torch.utils.data import (DataLoader, TensorDataset)


class SinglePathPromptDataLoader(object):
    r"""
    Prompt数据加载器，包装原始数据集。输入数据首先使用Prompt模板进行包装，
    然后通过包装的分词器进行分词处理。

    该类专门用于处理单路径Prompt数据，支持多GPU训练和分布式训练。

    Args:
        dataset (:obj:`Dataset` or :obj:`List`): 数据集对象或包含输入样本的列表
        template (:obj:`Template`): Template类的派生类实例
        tokenizer (:obj:`PretrainedTokenizer`): 预训练分词器
        tokenizer_wrapper_class (:cls:`TokenizerWrapper`): 分词器包装器类
        max_seq_length (:obj:`str`, optional): 输入序列的最大长度，用于截断句子
        batch_size (:obj:`int`, optional): 数据加载器的批次大小
        teacher_forcing (:obj:`bool`, optional): 是否用目标文本填充mask，在训练生成模型时设为True
        decoder_max_length (:obj:`bool`, optional): 编码器-解码器模型的解码器最大长度
        predict_eos_token (:obj:`bool`, optional): 是否预测<eos>标记，建议在生成时设为True
        truncate_method (:obj:`bool`, optional): 截断方法，可选`head`, `tail`, `balanced`
        kwargs  :其他可能传递给分词器包装器的参数
    """

    def __init__(self,
                 dataset: Union[Dataset, List],
                 template: Template,
                 tokenizer: PreTrainedTokenizer,
                 tokenizer_wrapper_class: TokenizerWrapper,
                 verbalizer: Optional[Verbalizer] = None,
                 max_seq_length: Optional[str] = 512,
                 batch_size: Optional[int] = 1,
                 shuffle: Optional[bool] = False,
                 teacher_forcing: Optional[bool] = False,
                 decoder_max_length: Optional[int] = -1,
                 predict_eos_token: Optional[bool] = False,
                 truncate_method: Optional[str] = "tail",
                 drop_last: Optional[bool] = False,
                 multi_gpu: bool = False,
                 mode: str = "train",
                 **kwargs,
                 ):
        '''
        初始化SinglePathPromptDataLoader
        
        Args:
            dataset (Union[Dataset, List]): 原始数据集
            template (Template): Prompt模板
            tokenizer (PreTrainedTokenizer): 预训练分词器
            tokenizer_wrapper_class (TokenizerWrapper): 分词器包装器类
            verbalizer (Optional[Verbalizer]): Verbalizer对象，默认为None
            max_seq_length (Optional[str]): 最大序列长度，默认为512
            batch_size (Optional[int]): 批次大小，默认为1
            shuffle (Optional[bool]): 是否打乱数据，默认为False
            teacher_forcing (Optional[bool]): 是否使用teacher forcing，默认为False
            decoder_max_length (Optional[int]): 解码器最大长度，默认为-1
            predict_eos_token (Optional[bool]): 是否预测结束标记，默认为False
            truncate_method (Optional[str]): 截断方法，默认为"tail"
            drop_last (Optional[bool]): 是否丢弃最后一个不完整的批次，默认为False
            multi_gpu (bool): 是否使用多GPU训练，默认为False
            mode (str): 运行模式，"train"或"test"，默认为"train"
            **kwargs: 其他参数
        '''
        # 检查数据集是否具有必要的方法
        assert hasattr(dataset, "__iter__"), f"The dataset must have __iter__ method. dataset is {dataset}"
        assert hasattr(dataset, "__len__"), f"The dataset must have __len__ method. dataset is {dataset}"
        self.raw_dataset = dataset  # 原始数据集

        self.wrapped_dataset = []  # 包装后的数据集
        self.tensor_dataset = []   # 张量格式的数据集
        self.template = template   # Prompt模板
        self.verbalizer = verbalizer  # Verbalizer
        self.batch_size = batch_size  # 批次大小
        self.shuffle = shuffle        # 是否打乱数据
        self.teacher_forcing = teacher_forcing  # 是否使用teacher forcing

        # 获取分词器包装器的初始化参数
        tokenizer_wrapper_init_keys = signature(tokenizer_wrapper_class.__init__).args
        prepare_kwargs = {
            "max_seq_length": max_seq_length,
            "truncate_method": truncate_method,
            "decoder_max_length": decoder_max_length,
            "predict_eos_token": predict_eos_token,
            "tokenizer": tokenizer,
            **kwargs,
        }
        # 过滤出分词器包装器需要的参数
        to_pass_kwargs = {key: prepare_kwargs[key] for key in prepare_kwargs if key in tokenizer_wrapper_init_keys}

        # 初始化分词器包装器
        self.tokenizer_wrapper = tokenizer_wrapper_class(**to_pass_kwargs)

        # 检查模板是否具有必要的方法
        assert hasattr(self.template, 'wrap_one_example'), "Your prompt has no function variable \
                                                         named wrap_one_example"

        # 处理数据：包装和分词
        self.wrap()      # 包装数据
        self.tokenize()  # 分词处理

        print("start convert_features_to_dataset")
        # 将特征转换为数据集
        self.tensor_dataset = self.convert_features_to_dataset()
        # 如果使用多GPU，禁用数据打乱
        if multi_gpu:
            self.shuffle = False

        # 根据训练模式和是否使用多GPU设置采样器
        if mode == 'train' and multi_gpu:
            sampler = torch.utils.data.distributed.DistributedSampler(self.tensor_dataset)
        else:
            if self.shuffle:
                sampler = RandomSampler(self.tensor_dataset)
            else:
                sampler = None
                
        # 创建数据加载器
        self.dataloader = DataLoader(
            self.tensor_dataset,
            batch_size=self.batch_size,
            num_workers=2,
            sampler=sampler,
            collate_fn=SinglePathPromptDataLoader.collate_fct,
            drop_last=drop_last,
            pin_memory=True
        )

    def convert_features_to_dataset(self):
        '''
        将特征转换为TensorDataset格式
        
        Returns:
            TensorDataset: 转换后的张量数据集
        '''
        def convert_tensor_to_numpy(item):
            '''
            将张量转换为NumPy数组
            
            Args:
                item: 待转换的项目
                
            Returns:
                转换后的项目
            '''
            if isinstance(item, torch.Tensor):
                if item.dim() == 0:
                    pass
                elif item.dim() == 1:
                    item = item.cpu().detach().numpy()
            elif isinstance(item, str):
                item = int(item)
            else:
                pass
            return item

        # 提取各种特征并转换为张量
        all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
                                     dtype=torch.long)
        all_input_mask = torch.tensor([convert_tensor_to_numpy(f['attention_mask']) for f in self.tensor_dataset],
                                      dtype=torch.long)
        all_label_ids = torch.tensor([convert_tensor_to_numpy(f['label']) for f in self.tensor_dataset],
                                     dtype=torch.long)
        all_loss_ids = torch.tensor([convert_tensor_to_numpy(f['loss_ids']) for f in self.tensor_dataset],
                                    dtype=torch.long)
        all_guid_ids = torch.tensor([convert_tensor_to_numpy(f['guid']) for f in self.tensor_dataset], dtype=torch.long)
        
        # 创建TensorDataset
        tensor_dataset = TensorDataset(all_input_ids, all_input_mask, all_label_ids, all_loss_ids,
                                       all_guid_ids)
        return tensor_dataset

    @staticmethod
    def collate_fct(batch):
        r'''
        用于整理输入特征的函数，将一批数据整理成适当的格式

        Args:
            batch (:obj:`List[Union[Dict, InputFeatures]]`): 当前批次的数据

        Returns:
            :obj:`InputFeatures`: 返回当前批次数据的InputFeatures
        '''
        collate_batch = []
        for idx in range(len(batch[0])):
            collate_batch.append(torch.stack([i[idx] for i in batch]))
        return collate_batch
        # return_dict = {}
        # keys = ['input_ids', 'attention_mask', 'label', 'loss_ids', 'guid']
        # for i, key in enumerate(keys):
        #     return_dict[key] = torch.stack([data[i] for data in batch])
        # return InputFeatures(**return_dict)

    def wrap(self):
        r"""简单的接口，将样本传递给Prompt，并使用模板包装文本
        """
        # 检查数据集类型
        if isinstance(self.raw_dataset, Dataset) or isinstance(self.raw_dataset, List):
            assert len(self.raw_dataset) > 0, 'The dataset to be wrapped is empty.'
            # 遍历数据集，包装每个样本
            # for idx, example in tqdm(enumerate(self.raw_dataset),desc='Wrapping'):
            for idx, example in enumerate(self.raw_dataset):
                # 如果Verbalizer存在且具有wrap_one_example方法，则先用Verbalizer处理
                if self.verbalizer is not None and hasattr(self.verbalizer,
                                                           'wrap_one_example'):  # some verbalizer may also process the example.
                    example = self.verbalizer.wrap_one_example(example)
                # 使用模板包装样本
                wrapped_example = self.template.wrap_one_example(example)
                self.wrapped_dataset.append(wrapped_example)
        else:
            raise NotImplementedError

    def tokenize(self) -> None:
        r"""将包装后的文本传递给专门用于Prompt的分词器，
           分词器内部的真正PretrainedTokenizer是灵活的，例如AlBert, Bert, T5等
        """
        # 遍历包装后的数据集，进行分词处理
        for idx, wrapped_example in tqdm(enumerate(self.wrapped_dataset), desc='tokenizing'):
            # for idx, wrapped_example in enumerate(self.wrapped_dataset):
            # 使用分词器包装器对单个样本进行分词，并转换为张量
            inputfeatures = InputFeatures(
                **self.tokenizer_wrapper.tokenize_one_example(wrapped_example, self.teacher_forcing),
                **wrapped_example[1]).to_tensor()
            self.tensor_dataset.append(inputfeatures)

    def __len__(self):
        '''
        返回数据加载器的长度
        
        Returns:
            int: 数据加载器的长度
        '''
        return len(self.dataloader)

    def __iter__(self, ):
        '''
        返回数据加载器的迭代器
        
        Returns:
            iterator: 数据加载器的迭代器
        '''
        return self.dataloader.__iter__()


class MyPromptDataLoader(object):
    r"""
    Prompt数据加载器，包装原始数据集。输入数据首先使用Prompt模板进行包装，
    然后通过包装的分词器进行分词处理。

    Args:
        dataset (:obj:`Dataset` or :obj:`List`): 数据集对象或包含输入样本的列表
        template (:obj:`Template`): Template类的派生类实例
        tokenizer (:obj:`PretrainedTokenizer`): 预训练分词器
        tokenizer_wrapper_class (:cls:`TokenizerWrapper`): 分词器包装器类
        max_seq_length (:obj:`str`, optional): 输入序列的最大长度，用于截断句子
        batch_size (:obj:`int`, optional): 数据加载器的批次大小
        teacher_forcing (:obj:`bool`, optional): 是否用目标文本填充mask，在训练生成模型时设为True
        decoder_max_length (:obj:`bool`, optional): 编码器-解码器模型的解码器最大长度
        predict_eos_token (:obj:`bool`, optional): 是否预测<eos>标记，建议在生成时设为True
        truncate_method (:obj:`bool`, optional): 截断方法，可选`head`, `tail`, `balanced`
        kwargs  :其他可能传递给分词器包装器的参数
    """

    def __init__(self,
                 dataset: Union[Dataset, List],
                 template: Template,
                 tokenizer: PreTrainedTokenizer,
                 tokenizer_wrapper_class: TokenizerWrapper,
                 verbalizer: Optional[Verbalizer] = None,
                 max_seq_length: Optional[str] = 512,
                 batch_size: Optional[int] = 1,
                 shuffle: Optional[bool] = False,
                 teacher_forcing: Optional[bool] = False,
                 decoder_max_length: Optional[int] = -1,
                 predict_eos_token: Optional[bool] = False,
                 truncate_method: Optional[str] = "tail",
                 drop_last: Optional[bool] = False,
                 **kwargs,
                 ):
        '''
        初始化MyPromptDataLoader
        
        Args:
            dataset (Union[Dataset, List]): 原始数据集
            template (Template): Prompt模板
            tokenizer (PreTrainedTokenizer): 预训练分词器
            tokenizer_wrapper_class (TokenizerWrapper): 分词器包装器类
            verbalizer (Optional[Verbalizer]): Verbalizer对象，默认为None
            max_seq_length (Optional[str]): 最大序列长度，默认为512
            batch_size (Optional[int]): 批次大小，默认为1
            shuffle (Optional[bool]): 是否打乱数据，默认为False
            teacher_forcing (Optional[bool]): 是否使用teacher forcing，默认为False
            decoder_max_length (Optional[int]): 解码器最大长度，默认为-1
            predict_eos_token (Optional[bool]): 是否预测结束标记，默认为False
            truncate_method (Optional[str]): 截断方法，默认为"tail"
            drop_last (Optional[bool]): 是否丢弃最后一个不完整的批次，默认为False
            **kwargs: 其他参数
        '''
        # 检查数据集是否具有必要的方法
        assert hasattr(dataset, "__iter__"), f"The dataset must have __iter__ method. dataset is {dataset}"
        assert hasattr(dataset, "__len__"), f"The dataset must have __len__ method. dataset is {dataset}"
        self.raw_dataset = dataset  # 原始数据集

        self.wrapped_dataset = []   # 包装后的数据集
        self.tensor_dataset = []    # 张量格式的数据集
        self.template = template    # Prompt模板
        self.verbalizer = verbalizer  # Verbalizer
        self.batch_size = batch_size  # 批次大小
        self.shuffle = shuffle        # 是否打乱数据
        self.teacher_forcing = teacher_forcing  # 是否使用teacher forcing

        # 获取分词器包装器的初始化参数
        tokenizer_wrapper_init_keys = signature(tokenizer_wrapper_class.__init__).args
        prepare_kwargs = {
            "max_seq_length": max_seq_length,
            "truncate_method": truncate_method,
            "decoder_max_length": decoder_max_length,
            "predict_eos_token": predict_eos_token,
            "tokenizer": tokenizer,
            **kwargs,
        }
        # 过滤出分词器包装器需要的参数
        to_pass_kwargs = {key: prepare_kwargs[key] for key in prepare_kwargs if key in tokenizer_wrapper_init_keys}

        # 初始化分词器包装器
        self.tokenizer_wrapper = tokenizer_wrapper_class(**to_pass_kwargs)

        # 检查模板是否具有必要的方法
        assert hasattr(self.template, 'wrap_one_example'), "Your prompt has no function variable \
                                                         named wrap_one_example"

        # 处理数据：包装和分词
        self.wrap()      # 包装数据
        self.tokenize()  # 分词处理

        # 根据是否打乱数据设置采样器
        if self.shuffle:
            sampler = RandomSampler(self.tensor_dataset)
        else:
            sampler = None

        # 创建数据加载器
        self.dataloader = DataLoader(
            self.tensor_dataset,
            batch_size=self.batch_size,
            sampler=sampler,
            collate_fn=MyPromptDataLoader.collate_fct,
            drop_last=drop_last,
        )

    def wrap(self):
        r"""简单的接口，将样本传递给Prompt，并使用模板包装文本
        """
        # 检查数据集类型
        if isinstance(self.raw_dataset, Dataset) or isinstance(self.raw_dataset, List):
            assert len(self.raw_dataset) > 0, 'The dataset to be wrapped is empty.'
            # 遍历数据集，包装每个样本
            # for idx, example in tqdm(enumerate(self.raw_dataset),desc='Wrapping'):
            for idx, example in enumerate(self.raw_dataset):
                # 如果Verbalizer存在且具有wrap_one_example方法，则先用Verbalizer处理
                if self.verbalizer is not None and hasattr(self.verbalizer,
                                                           'wrap_one_example'):  # some verbalizer may also process the example.
                    example = self.verbalizer.wrap_one_example(example)
                # 使用模板包装样本
                wrapped_example = self.template.wrap_one_example(example)
                self.wrapped_dataset.append(wrapped_example)
        else:
            raise NotImplementedError

    def tokenize(self) -> None:
        r"""将包装后的文本传递给专门用于Prompt的分词器，
           分词器内部的真正PretrainedTokenizer是灵活的，例如AlBert, Bert, T5等
        """
        # 遍历包装后的数据集，进行分词处理
        for idx, wrapped_example in tqdm(enumerate(self.wrapped_dataset), desc='tokenizing'):
            # for idx, wrapped_example in enumerate(self.wrapped_dataset):
            # 使用分词器包装器对单个样本进行分词，并转换为张量
            inputfeatures = InputFeatures(
                **self.tokenizer_wrapper.tokenize_one_example(wrapped_example, self.teacher_forcing),
                **wrapped_example[1]).to_tensor()
            self.tensor_dataset.append(inputfeatures)

    def __len__(self):
        '''
        返回数据加载器的长度
        
        Returns:
            int: 数据加载器的长度
        '''
        return len(self.dataloader)

    def __iter__(self, ):
        '''
        返回数据加载器的迭代器
        
        Returns:
            iterator: 数据加载器的迭代器
        '''
        return self.dataloader.__iter__()

    @staticmethod
    def collate_fct(batch: List):
        r'''
        用于整理输入特征的函数，将一批数据整理成适当的格式

        Args:
            batch (:obj:`List[Union[Dict, InputFeatures]]`): 当前批次的数据

        Returns:
            :obj:`InputFeatures`: 返回当前批次数据的InputFeatures
        '''
        elem = batch[0]
        return_dict = {}
        # 遍历批次中的每个元素，根据键名进行不同的处理
        for key in elem:
            if key == "encoded_tgt_text":
                # 对于编码的目标文本，保持为列表形式
                return_dict[key] = [d[key] for d in batch]
            elif key == "label":
                # 对于标签，保持为列表形式
                batch_labels = [d[key] for d in batch]
                return_dict[key] = batch_labels
            else:
                # 对于其他键，尝试使用默认的整理函数
                try:
                    return_dict[key] = default_collate([d[key] for d in batch])
                except:
                    print(f"key{key}\n d {[batch[i][key] for i in range(len(batch))]} ")

        # 返回整理后的InputFeatures对象
        return InputFeatures(**return_dict)