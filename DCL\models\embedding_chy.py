'''
层次化动词提示分类模型模块
实现基于Prompt的层次文本分类模型，支持对比学习和约束损失，专门用于生成文本嵌入表示

该模块实现了用于层次文本分类的Prompt模型，主要特点包括：
1. 支持多个层级的Verbalizer，每个层级对应分类任务的一个层次
2. 集成多种损失函数，包括标准分类损失、MLM损失、对比损失和约束损失
3. 实现了层次结构感知的标签嵌入初始化
4. 提供评估方法，支持基于路径的评估指标计算
5. 支持模型状态的保存和加载
6. 特别适用于生成文本嵌入表示，用于后续的检索式上下文学习
'''

import openprompt
from openprompt import PromptForClassification
from openprompt.prompt_base import Template, Verbalizer
import torch
from typing import List
from transformers.utils.dummy_pt_objects import PreTrainedModel
from tqdm import tqdm
from transformers import BertTokenizer
from util.utils import _mask_tokens
from util.eval import compute_score, compute_based_on_path
from models.loss import constraint_multi_depth_loss_func, flat_contrastive_loss_func, sim
import pickle

class HierVerbPromptForClassification(PromptForClassification):
    '''
    层次化动词提示分类模型类，继承自PromptForClassification
    
    该类扩展了OpenPrompt的PromptForClassification，专门用于处理层次文本分类任务，
    支持多层级的Verbalizer、多种损失函数组合以及层次结构感知的标签嵌入初始化。
    特别适用于生成用于检索式上下文学习的文本嵌入表示。
    '''
    def __init__(self,
                 plm: PreTrainedModel,
                 template: Template,
                 verbalizer_list: List[Verbalizer],
                 freeze_plm: bool = False,
                 plm_eval_mode: bool = False,
                 verbalizer_mode=False,
                 args=None,
                 processor=None,
                 logger=None,
                 use_cuda=True
                 ):
        '''
        初始化层次化动词提示分类模型
        
        Args:
            plm (PreTrainedModel): 预训练语言模型
            template (Template): 模板对象，用于构建输入
            verbalizer_list (List[Verbalizer]): 多个层级的Verbalizer列表
            freeze_plm (bool): 是否冻结预训练语言模型参数，默认为False
            plm_eval_mode (bool): 是否在评估模式下使用预训练语言模型，默认为False
            verbalizer_mode (bool): Verbalizer模式，默认为False
            args: 命令行参数对象，默认为None
            processor: 数据处理器，默认为None
            logger: 日志记录器，默认为None
            use_cuda (bool): 是否使用CUDA，默认为True
        '''
        super().__init__(plm=plm, template=template, verbalizer=verbalizer_list[0], freeze_plm=freeze_plm,
                         plm_eval_mode=plm_eval_mode)
        self.verbalizer_list = verbalizer_list  # 多层级Verbalizer列表
        self.verbLength = len(self.verbalizer_list)  # Verbalizer数量，对应层次深度
        self.verbalizer_mode = verbalizer_mode  # Verbalizer模式标识

        # 为每个层级的Verbalizer设置属性
        for idx, verbalizer in enumerate(self.verbalizer_list):
            self.__setattr__(f"verbalizer{idx}", verbalizer)
        self.args = args  # 命令行参数
        self.processor = processor  # 数据处理器
        self.use_cuda = use_cuda  # 是否使用CUDA
        self.logger = logger  # 日志记录器
        if self.args.mean_verbalizer:
            self.init_embeddings()  # 初始化标签嵌入
        # 标志位，用于控制是否打印相关信息
        self.flag_constraint_loss = False  # 约束损失标志
        self.flag_contrastive_loss = False  # 对比损失标志
        self.flag_contrastive_logits = False  # 对比logits标志

    def forward(self, batch) -> torch.Tensor:
        r"""
        前向传播函数，计算模型输出和损失值
        
        该函数实现了模型的前向传播过程，包括:
        1. 通过prompt模型获取输出
        2. 提取mask位置的输出
        3. 通过各层级Verbalizer处理输出得到logits
        4. 在训练模式下计算各种损失函数
        5. 返回logits和损失值

        Args:
            batch (:obj:`Union[Dict, InputFeatures]`): 输入批次数据，包含input_ids、attention_mask等

        Returns:
            :obj:`torch.Tensor`: 训练模式下返回logits、总损失和损失详情，推理模式下返回logits和输出嵌入
        """

        # 初始化各种损失值
        loss = 0  # 总损失
        loss_details = [0, 0, 0, 0]  # 损失详情：[分类损失, MLM损失, 约束损失, 对比损失]
        lm_loss = None  # MLM损失
        constraint_loss = None  # 约束损失
        contrastive_loss = None  # 对比损失
        args = self.args  # 获取参数
        
        # 如果启用dropout相似性并且处于训练模式
        if args.use_dropout_sim and self.training:
            if not self.flag_contrastive_logits:
                print("using contrastive_logits")
                self.flag_contrastive_logits = True
            # 构建对比学习批次数据
            contrastive_batch = dict()
            for k, v in batch.items():
                tmp = []
                for i in v:
                    tmp.append(i)
                    tmp.append(i)
                contrastive_batch[k] = torch.stack(tmp) if isinstance(tmp[0], torch.Tensor) else tmp
                contrastive_batch[k] = contrastive_batch[k].to("cuda:0")
            batch = contrastive_batch

        # 通过prompt模型获取输出
        outputs = self.prompt_model(batch)
        outputs = self.verbalizer_list[0].gather_outputs(outputs)
        # outputs = self.verbalizer1.gather_outputs(outputs)

        # 提取mask位置的输出
        if isinstance(outputs, tuple):
            outputs_at_mask = [self.extract_at_mask(output, batch) for output in outputs]
        else:
            outputs_at_mask = self.extract_at_mask(outputs, batch)
            
        # 通过各层级Verbalizer处理输出得到logits
        logits = []
        for idx in range(self.verbLength):
            label_words_logtis = self.__getattr__(f"verbalizer{idx}").process_outputs(outputs_at_mask[:, idx, :],
                                                                                      batch=batch)
            logits.append(label_words_logtis)

        # 训练模式下的损失计算
        if self.training:
            labels = batch['label']  # 获取标签

            # 构建层次标签
            hier_labels = []
            hier_labels.insert(0, labels)  # 插入叶节点标签
            # 从叶节点向上构建各层级标签
            for idx in range(args.depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(labels)  # 初始化当前层级标签
                for i in range(len(labels)):
                    # 根据层次映射关系获取当前层级标签
                    # cur_depth_labels[i] = label1_to_label0_mapping[labels[i].tolist()]
                    cur_depth_labels[i] = self.processor.hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)  # 插入当前层级标签

            ## MLM损失计算
            if args.lm_training:
                # 获取输入ID并进行mask处理
                input_ids = batch['input_ids']
                input_ids, labels = _mask_tokens(self.tokenizer, input_ids.cpu())

                # 构建MLM输入
                lm_inputs = {"input_ids": input_ids, "attention_mask": batch['attention_mask'], "labels": labels}

                # 将输入移到指定设备
                for k, v in lm_inputs.items():
                    if v is not None:
                        lm_inputs[k] = v.to(self.device)
                # 计算MLM损失
                lm_loss = self.plm(**lm_inputs)[0]

            # 根据是否多标签分类选择损失函数
            if args.multi_label:
                loss_func = torch.nn.BCEWithLogitsLoss()  # 多标签损失
            else:
                loss_func = torch.nn.CrossEntropyLoss()  # 单标签损失

            # 计算各层级的分类损失
            for idx, cur_depth_label in enumerate(hier_labels):
                cur_depth_logits = logits[idx]
                if args.multi_label:
                    # 多标签情况下的标签处理
                    cur_multi_label = torch.zeros_like(cur_depth_logits)

                    for i in range(cur_multi_label.shape[0]):
                        cur_multi_label[i][cur_depth_label[i]] = 1
                    cur_depth_label = cur_multi_label
                # 累加各层级分类损失
                loss += loss_func(cur_depth_logits, cur_depth_label)

            loss_details[0] += loss.item()  # 层级二loss
            ## hierarchical constraint chain
            # 计算约束损失
            if args.constraint_loss:
                if not self.flag_constraint_loss:
                    print(f"using constraint loss with cs_mode {args.cs_mode} eval_mode {args.eval_mode}")
                    self.flag_constraint_loss = True
                constraint_loss = constraint_multi_depth_loss_func(logits, loss_func, hier_labels, self.processor, args,
                                                                   use_cuda=self.use_cuda, mode=args.cs_mode)
            ## flat contrastive loss
            # 计算对比损失
            if args.contrastive_loss:
                if not self.flag_contrastive_loss:
                    print(f"using flat contrastive loss with alpha {args.contrastive_alpha}")
                    if args.use_dropout_sim:
                        print("using use_dropout_sim")
                    self.flag_contrastive_loss = True
                # 从batch中提取input_ids作为文本内容的代理
                text_contents = None
                if 'input_ids' in batch:
                    # 将input_ids转换为字符串用于比较文本相似性
                    text_contents = [str(ids.tolist()) for ids in batch['input_ids']]

                # 注意：这里的参数顺序与hierVerb.py不同，不需要使用到label_sim参数
                contrastive_loss = flat_contrastive_loss_func(None, hier_labels, self.processor,
                                                                            outputs_at_mask,
                                                                            imbalanced_weight=args.imbalanced_weight,
                                                                            contrastive_level=args.contrastive_level,
                                                                            imbalanced_weight_reverse=args.imbalanced_weight_reverse,
                                                                            depth=args.depth,
                                                                            use_cuda=self.use_cuda,
                                                                            text_contents=text_contents)

            ####
            # 计算另一种对比损失（基于标签相同性）
            cur_batch_size = outputs_at_mask.shape[0]
            contrastive_loss = 0
            for idx, cur_depth_label in enumerate(hier_labels):
                sim_score = sim(outputs_at_mask[:,idx,:], outputs_at_mask[:,idx,:])
                sim_score = torch.exp(sim_score)
                cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)
                for i in range(len(cur_depth_label)):
                    for j in range(len(cur_depth_label)):
                        if cur_depth_label[i] == cur_depth_label[j]:
                            cur_hier_matrix[i][j] = 1
                        else:
                            cur_hier_matrix[i][j] = 0
                
                pos_sim = sim_score[cur_hier_matrix != 0].sum()
                neg_sim = sim_score[cur_hier_matrix == 0].sum()
                contrastive_loss += - torch.log(pos_sim / (pos_sim + neg_sim))

            #####
            # 组合各种损失
            if lm_loss is not None:
                if args.lm_alpha != -1:
                    # 按权重组合分类损失和MLM损失
                    loss = loss * args.lm_alpha + (1 - args.lm_alpha) * lm_loss
                else:
                    loss += lm_loss
                loss_details[1] += lm_loss.item()  # 记录MLM损失

            if constraint_loss is not None:
                if args.constraint_alpha != -1:
                    # 按权重组合分类损失和约束损失
                    loss = loss * args.constraint_alpha + (1 - args.constraint_alpha) * constraint_loss
                else:
                    loss += constraint_loss
                loss_details[2] += constraint_loss.item()  # 记录约束损失

            if contrastive_loss is not None:
                if args.contrastive_alpha != -1:
                    # 按权重组合分类损失和对比损失
                    # loss = loss * contrastive_alpha + (1 - contrastive_alpha) * contrastive_loss
                    loss += (1 - args.contrastive_alpha) * contrastive_loss
                else:
                    loss += contrastive_loss
                loss_details[3] += contrastive_loss.item()  # 记录对比损失

            # 返回logits、总损失和损失详情
            return logits, loss, loss_details
        else:
            # 推理模式下返回logits和输出嵌入
            return logits, outputs_at_mask

    def init_embeddings(self):
        '''
        初始化标签嵌入向量
        
        通过预训练语言模型获取标签词的嵌入向量，并根据层次结构进行平均处理，
        使得父节点标签的嵌入向量能够包含子节点的信息
        '''
        self.print_info("using label emb for soft verbalizer")

        label_emb_list = []  # 标签嵌入列表
        # 遍历各层级，获取标签嵌入
        for idx in range(self.args.depth):
            label_dict = self.processor.label_list[idx]  # 获取当前层级标签列表
            label_dict = dict({idx: v for idx, v in enumerate(label_dict)})  # 构建索引到标签的映射
            label_dict = {i: self.tokenizer.encode(v) for i, v in label_dict.items()}  # 对标签进行编码
            label_emb = []  # 当前层级标签嵌入
            input_embeds = self.plm.get_input_embeddings()  # 获取输入嵌入层

            # 计算每个标签的嵌入向量（词向量的平均）
            for i in range(len(label_dict)):
                label_emb.append(
                    input_embeds.weight.index_select(0, torch.tensor(label_dict[i], device=self.device)).mean(dim=0))
            label_emb = torch.stack(label_emb)  # 堆叠标签嵌入
            label_emb_list.append(label_emb)
            
        # 如果启用层次平均，则对标签嵌入进行层次结构处理
        if self.args.use_hier_mean:
            # 从叶节点向上处理各层级
            for depth_idx in range(self.args.depth - 2, -1, -1):
                cur_label_emb = label_emb_list[depth_idx]  # 当前层级标签嵌入
                cur_depth_length = len(self.processor.label_list[depth_idx])  # 当前层级标签数量
                # 对每个标签，将其嵌入向量与子节点嵌入向量进行平均
                for i in range(cur_depth_length):
                    cur_label_emb[i] = cur_label_emb[i] + label_emb_list[depth_idx + 1][
                                                          self.processor.hier_mapping[depth_idx][0][i], :].mean(dim=0)
                label_emb_list[depth_idx] = cur_label_emb

                # cur_label_emb = label_emb_list[depth_idx]
                # cur_depth_length = len(self.processor.label_list[depth_idx])

                # for i in range(cur_depth_length):
                #     # 检查映射索引是否有效
                #     mapped_indices = self.processor.hier_mapping[depth_idx][0][i]
                #     next_level_size = len(label_emb_list[depth_idx + 1])
                #     # 确保所有映射索引都在下一层级的范围内
                #     if not all(0 <= idx < next_level_size for idx in mapped_indices):
                #         self.print_info(f"Warning: Invalid mapping at depth {depth_idx}, index {i} -> {mapped_indices}")
                #         continue
                #     # 计算有效索引的平均值
                #     cur_label_emb[i] += label_emb_list[depth_idx + 1][mapped_indices, :].mean(dim=0)
                # label_emb_list[depth_idx] = cur_label_emb

        # 将处理后的标签嵌入赋值给各层级Verbalizer
        for idx in range(self.args.depth):
            label_emb = label_emb_list[idx]
            self.print_info(f"depth {idx}: {label_emb.shape}")
            if "0.1.2" in openprompt.__path__[0]:
                self.__getattr__(f"verbalizer{idx}").head_last_layer.weight.data = label_emb
                self.__getattr__(f"verbalizer{idx}").head_last_layer.weight.data.requires_grad = True
            else:
                getattr(self.__getattr__(f"verbalizer{idx}").head.predictions,
                        'decoder').weight.data = label_emb
                getattr(self.__getattr__(f"verbalizer{idx}").head.predictions,
                        'decoder').weight.data.requires_grad = True

    def evaluate(self, dataloader, processor, desc="Valid", mode=0, device="cuda:0", args=None):
        '''
        模型评估函数，同时生成文本嵌入表示用于后续检索
        
        在验证或测试集上评估模型性能，支持多种评估模式和基于路径的评估指标计算。
        与标准评估不同，该函数还会生成并保存文本嵌入表示，用于检索式上下文学习。

        Args:
            dataloader: 数据加载器
            processor: 数据处理器
            desc (str): 进度条描述，默认为"Valid"
            mode (int): 评估模式，默认为0
            device (str): 设备，默认为"cuda:0"
            args: 参数，默认为None
            
        Returns:
            dict: 评估结果，包含各种指标
        '''
        self.eval()  # 设置为评估模式
        pred = []  # 预测结果
        truth = []  # 真实标签
        # 用于存储嵌入表示和标签的字典
        output = {'embedding':[],'label':[]}
        pbar = tqdm(dataloader, desc=desc)  # 进度条
        hier_mapping = processor.hier_mapping  # 层次映射关系
        depth = len(hier_mapping) + 1  # 层次深度
        all_length = len(processor.all_labels)  # 所有标签数量
        # 遍历数据批次
        for step, batch in enumerate(pbar):
            # 将批次数据移到指定设备
            if hasattr(batch, 'cuda'):
                batch = batch.cuda()
            else:
                batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3]}
            # 获取模型输出
            logits, leaf_embed = self(batch)
            leaf_labels = batch['label']  # 叶节点标签
            
            # 收集嵌入表示和标签，用于后续保存
            for idx in range(leaf_embed.shape[0]):
                output['embedding'].append(leaf_embed[idx].detach().cpu().numpy().tolist())
                output['label'].append(leaf_labels[idx].detach().cpu().numpy().tolist())
                
            # 构建层次标签
            hier_labels = []
            hier_labels.insert(0, leaf_labels)
            # 从叶节点向上构建各层级标签
            for idx in range(depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(leaf_labels)
                for i in range(len(leaf_labels)):
                    cur_depth_labels[i] = hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)

                # cur_depth_labels = torch.zeros_like(leaf_labels)
                # for i in range(len(leaf_labels)):
                #     label_index = hier_labels[0][i].tolist()
                #     if label_index in self.processor.hier_mapping[idx][1]:
                #         cur_depth_labels[i] = self.processor.hier_mapping[idx][1][label_index]
                #     else:
                #         # 处理无效索引：记录警告/使用默认值
                #         cur_depth_labels[i] = 0  
                #     # cur_depth_labels[i] = hier_mapping[idx][1][hier_labels[0][i].tolist()]
                # hier_labels.insert(0, cur_depth_labels)

            # 获取叶节点logits
            if isinstance(logits, list):
                leaf_logits = logits[-1]
            elif isinstance(logits, torch.Tensor):
                leaf_logits = logits[:, -1, :]
            leaf_logits = torch.softmax(leaf_logits, dim=-1)  # 应用softmax
            batch_preds = []  # 批次预测结果
            batch_golds = []  # 批次真实标签

            # 获取叶节点预测结果
            leaf_preds = torch.argmax(leaf_logits, dim=-1).cpu().tolist()
            leaf_labels = leaf_labels.cpu().tolist()

            batch_preds.insert(0, leaf_preds)
            batch_golds.insert(0, leaf_labels)

            batch_s = leaf_logits.shape[0]  # 批次大小
            flat_slot2value = processor.flat_slot2value  # 扁平slot到值的映射
            hier_logits = []  # 层次logits
            hier_logits.insert(0, leaf_logits)

            # 从叶节点向上计算各层级预测结果
            for depth_idx in range(depth - 2, -1, -1):
                ori_logits = torch.softmax(logits[depth_idx], dim=-1)  # 原始logits

                # 根据标签数量选择不同的处理方式
                if ori_logits.shape[-1] != all_length:
                    cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))
                    for i in range(cur_logits.shape[-1]):
                        cur_logits[:, i] = torch.mean(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                    # cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))
                    # for i in range(cur_logits.shape[-1]):
                    #     # 检查映射索引是否存在且有效
                    #     if depth_idx not in hier_mapping or \
                    #        i not in hier_mapping[depth_idx][0]:
                    #         # self.print_info(f"Warning: Missing mapping at depth {depth_idx}, index {i}")
                    #         continue
                            
                    #     mapped_indices = hier_mapping[depth_idx][0][i]
                else:
                    cur_logits = torch.zeros(batch_s, all_length)
                    cd_labels = processor.depth2label[depth_idx]
                    for i in range(all_length):
                        if i in cd_labels:
                            cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)

                cur_logits = cur_logits.to(device)

                # for i in range(cur_label_size):
                #     cur_logits[:, i] = torch.sum(hier_logits[0][:, cur_mapping[0][i]], dim=-1)
                # 根据模式选择不同的logits组合方式
                if mode == 0:
                    softmax_label_logits = ori_logits
                elif mode == 1:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1)
                elif mode == 2:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1) + ori_logits
                    softmax_label_logits = torch.softmax(softmax_label_logits, dim=-1)

                # 获取当前层级预测结果
                cur_preds = torch.argmax(softmax_label_logits, dim=-1).cpu().tolist()
                cur_golds = hier_labels[depth_idx].cpu().tolist()

                hier_logits.insert(0, softmax_label_logits)
                batch_preds.insert(0, cur_preds)
                batch_golds.insert(0, cur_golds)
            # 转置批次预测和真实标签
            batch_preds = torch.tensor(batch_preds).transpose(1, 0).cpu().tolist()
            batch_golds = torch.tensor(batch_golds).transpose(1, 0).cpu().tolist()

            # 构建最终预测和真实标签
            for i in range(batch_s):
                sub_preds = []
                sub_golds = []
                prev_label_size = 0
                for depth_idx in range(depth):
                    # 第0层直接添加
                    if depth_idx == 0:
                        sub_preds.append(batch_preds[i][depth_idx])
                        sub_golds.append(batch_golds[i][depth_idx])

                        continue
                    # 其他层需要加上前一层的标签数量作为偏移
                    prev_mapping = hier_mapping[depth_idx - 1]
                    prev_label_size = len(prev_mapping[0]) + prev_label_size
                    if leaf_logits.shape[-1] == all_length:
                        sub_preds.append(batch_preds[i][depth_idx])
                    else:
                        sub_preds.append(batch_preds[i][depth_idx] + prev_label_size)
                    sub_golds.append(batch_golds[i][depth_idx] + prev_label_size)
                pred.append(sub_preds)
                truth.append(sub_golds)

        # 构建标签字典
        label_dict = dict({idx: label for idx, label in enumerate(processor.all_labels)})      
        # 保存嵌入表示和标签到pickle文件
        pickle.dump(output, open('_'+str(args.shot)+'shot_none_'+str(args.seed)+'_embed_doc_' + str(args.label_description) + '.pkl',"wb"))
        # 计算评估分数
        scores = compute_score(pred, truth, label_dict)
        return scores  # 返回评估结果

    def print_info(self, info):
        '''
        打印信息的辅助函数
        
        Args:
            info (str): 要打印的信息
        '''
        if self.logger is not None:
            self.logger.info(info)
        else:
            print(info)

    def state_dict(self, *args, **kwargs):
        """ 
        保存模型状态字典，包括模板、预训练语言模型和各层级Verbalizer的状态
        
        Returns:
            dict: 模型状态字典
        """
        _state_dict = {}
        # 如果没有冻结预训练语言模型，则保存其状态
        if not self.prompt_model.freeze_plm:
            _state_dict['plm'] = self.plm.state_dict(*args, **kwargs)
        # 保存模板状态
        _state_dict['template'] = self.template.state_dict(*args, **kwargs)
        # 保存各层级Verbalizer状态
        for idx in range(self.verbLength):
            _state_dict[f'verbalizer{idx}'] = self.__getattr__(f"verbalizer{idx}").state_dict(*args, **kwargs)
        return _state_dict

    def load_state_dict(self, state_dict, *args, **kwargs):
        """ 
        加载模型状态字典
        
        Args:
            state_dict (dict): 模型状态字典
        """
        # 加载预训练语言模型状态
        if 'plm' in state_dict and not self.prompt_model.freeze_plm:
            self.plm.load_state_dict(state_dict['plm'], *args, **kwargs)
        # 加载模板状态
        self.template.load_state_dict(state_dict['template'], *args, **kwargs)

        # 加载各层级Verbalizer状态
        for idx in range(self.verbLength):
            self.__getattr__(f"verbalizer{idx}").load_state_dict(state_dict[f'verbalizer{idx}'], *args, **kwargs)