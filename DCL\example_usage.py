#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比学习中排除相同文本内容样本的使用示例

这个示例展示了如何在对比学习过程中排除具有相同文本内容的样本，
避免在负样本采样时产生冲突的训练信号。
"""

import torch
from models.loss import flat_contrastive_loss_func

def example_usage():
    """
    演示如何使用修改后的flat_contrastive_loss_func函数
    """
    
    # 模拟数据
    batch_size = 4
    depth = 2
    hidden_dim = 768
    
    # 模拟输出表示
    output_at_mask = torch.randn(batch_size, depth, hidden_dim)
    
    # 模拟层次化标签
    hier_labels = [
        [0, 1, 0, 1],  # 第一层标签
        [0, 0, 1, 1]   # 第二层标签
    ]
    
    # 模拟文本内容 - 注意样本0和样本2有相同的文本内容
    text_contents = [
        "这是一个测试文本",      # 样本0
        "这是另一个不同的文本",   # 样本1  
        "这是一个测试文本",      # 样本2 - 与样本0相同
        "第三个独特的文本"       # 样本3
    ]
    
    # 模拟标签相似度矩阵
    label_sim = torch.randn(batch_size, batch_size)
    
    # 模拟processor（简化版）
    class MockProcessor:
        pass
    
    processor = MockProcessor()
    
    print("=== 对比学习损失计算示例 ===")
    print(f"批次大小: {batch_size}")
    print(f"层次深度: {depth}")
    print(f"文本内容: {text_contents}")
    print(f"层次标签: {hier_labels}")
    
    # 计算对比学习损失
    try:
        contrastive_loss = flat_contrastive_loss_func(
            label_sim=label_sim,
            hier_labels=hier_labels,
            processor=processor,
            output_at_mask=output_at_mask,
            imbalanced_weight=False,
            depth=depth,
            contrastive_level=1,
            imbalanced_weight_reverse=False,
            use_cuda=False,
            text_contents=text_contents  # 新增的参数
        )
        
        print(f"\n对比学习损失: {contrastive_loss.item():.4f}")
        print("\n✅ 成功计算对比学习损失！")
        print("📝 注意：样本0和样本2具有相同文本内容，已在负样本采样中被排除")
        
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        return False
    
    return True

def demonstrate_text_content_filtering():
    """
    演示文本内容过滤的效果
    """
    print("\n=== 文本内容过滤演示 ===")
    
    # 示例：多个样本具有相同文本但不同标签
    samples = [
        {"text": "机器学习是人工智能的一个分支", "labels": [0, 1]},  # 计算机科学 -> 人工智能
        {"text": "深度学习使用神经网络", "labels": [0, 2]},        # 计算机科学 -> 深度学习  
        {"text": "机器学习是人工智能的一个分支", "labels": [0, 3]},  # 计算机科学 -> 机器学习 (相同文本!)
        {"text": "自然语言处理处理文本数据", "labels": [0, 4]}      # 计算机科学 -> NLP
    ]
    
    print("样本信息:")
    for i, sample in enumerate(samples):
        print(f"  样本{i}: '{sample['text']}' -> 标签{sample['labels']}")
    
    print("\n🔍 检测到的相同文本内容:")
    text_contents = [sample["text"] for sample in samples]
    for i in range(len(text_contents)):
        for j in range(i+1, len(text_contents)):
            if text_contents[i] == text_contents[j]:
                print(f"  样本{i} 和 样本{j}: '{text_contents[i]}'")
    
    print("\n💡 解决方案:")
    print("  - 在对比学习中，样本0和样本2不会被互相作为负样本")
    print("  - 这避免了相同文本内容但不同标签的冲突训练信号")
    print("  - 提高了对比学习的训练质量和模型性能")

if __name__ == "__main__":
    print("🚀 对比学习文本内容过滤示例")
    print("=" * 50)
    
    # 运行基本使用示例
    success = example_usage()
    
    if success:
        # 演示文本内容过滤
        demonstrate_text_content_filtering()
        
        print("\n" + "=" * 50)
        print("📚 使用说明:")
        print("1. 在调用flat_contrastive_loss_func时传入text_contents参数")
        print("2. text_contents应该是一个包含每个样本文本内容的列表")
        print("3. 函数会自动排除相同文本内容的样本作为负样本")
        print("4. 这有助于避免对比学习中的冲突训练信号")
    else:
        print("\n❌ 示例运行失败，请检查代码实现")
