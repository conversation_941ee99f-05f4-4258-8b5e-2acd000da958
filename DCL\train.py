'''
层次文本分类训练脚本，实现基于Prompt的少样本层次文本分类模型训练
'''

# 导入标准库和第三方库
import datetime                     # 用于记录训练时间
import logging                      # 配置日志记录
from tqdm import tqdm               # 显示训练进度条
import os                           # 操作系统功能，如创建目录
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"  # 设置CUDA同步执行，便于调试
import torch                        # PyTorch深度学习框架
import argparse                     # 解析命令行参数
import openprompt                   # OpenPrompt框架，用于提示学习
from openprompt.utils.reproduciblity import set_seed  # 设置随机种子以保证可复现性
from openprompt.prompts import SoftVerbalizer, ManualTemplate  # 提示学习中的模板和verbalizer

# 导入自定义模块
from models.hierVerb import HierVerbPromptForClassification  # 自定义的层次分类模型

from processor import PROCESSOR     # 数据处理器
from processor_des import PROCESSOR1  # 另一种数据处理器

from util.utils import load_plm_from_config, print_info  # 工具函数
from util.data_loader import SinglePathPromptDataLoader  # 自定义数据加载器
from transformers import AdamW, get_linear_schedule_with_warmup  # 优化器和学习率调度器


# 配置日志格式和级别
logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)  # 创建日志记录器实例

use_cuda = True  # 全局变量，标识是否使用CUDA


def main():
    '''
    主训练函数，负责整个训练流程的控制
    包括参数解析、数据加载、模型初始化、训练循环和评估等步骤
    '''
    # 记录程序开始时间
    start_time = datetime.datetime.now()
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser("")

    # 模型相关参数
    parser.add_argument("--model", type=str, default='bert', help="预训练模型类型")
    parser.add_argument("--model_name_or_path", default='/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese', help="预训练模型路径")
    parser.add_argument("--result_file", type=str, default="few_shot_train.txt", help="结果保存文件名")

    # 多mask相关参数
    parser.add_argument("--multi_mask", type=int, default=1, help="是否使用多mask模板")
    parser.add_argument("--dropout", default=0.1, type=float, help="dropout率")
    parser.add_argument("--shuffle", default=0, type=int, help="是否打乱数据集")
    parser.add_argument("--contrastive_logits", default=1, type=int, help="是否使用对比学习logits")
    parser.add_argument("--constraint_loss", default=0, type=int, help="是否使用约束损失")
    parser.add_argument("--cs_mode", default=0, type=int, help="对比学习模式")
    parser.add_argument("--dataset", default="wos", type=str, help="数据集名称")
    parser.add_argument("--eval_mode", default=0, type=int, help="评估模式")
    parser.add_argument("--use_hier_mean", default=1, type=int, help="是否使用层次平均")
    parser.add_argument("--freeze_plm", default=0, type=int, help="是否冻结预训练模型参数")
    
    # 多标签和多动词相关参数
    parser.add_argument("--multi_label", default=0, type=int, help="是否使用多标签")
    parser.add_argument("--multi_verb", default=1, type=int, help="是否使用多动词")

    # 学习率调度器参数
    parser.add_argument("--use_scheduler1", default=1, type=int, help="是否使用第一个学习率调度器")
    parser.add_argument("--use_scheduler2", default=1, type=int, help="是否使用第二个学习率调度器")

    # 约束损失参数  多标签分类、层次结构、对比学习相关的任务中会加入约束损失，来加强模型对结构/关系的建模能力
    parser.add_argument("--constraint_alpha", default=-1, type=float, help="约束损失权重")

    # 类别不平衡处理参数
    parser.add_argument("--imbalanced_weight", default=True, type=bool, help="是否使用权重处理类别不平衡")
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool, help="是否反向处理类别不平衡权重")

    # 设备参数
    parser.add_argument("--device", default=1, type=int, help="GPU设备ID，-1表示使用CPU")

    # 训练参数
    parser.add_argument("--lm_training", default=1, type=int, help="是否进行语言模型训练")
    parser.add_argument("--lr", default=5e-5, type=float, help="主学习率")
    parser.add_argument("--lr2", default=1e-4, type=float, help="第二学习率")
    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="最大梯度范数，用于梯度裁剪")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="最大序列长度")

    # 对比学习参数
    parser.add_argument("--use_new_ct", default=1, type=int, help="是否使用新的对比学习方法")
    parser.add_argument("--contrastive_loss", default=0, type=int, help="是否使用对比损失")
    parser.add_argument("--contrastive_alpha", default=0.99, type=float, help="对比损失权重")

    # 对比损失的权重衰减速度和dropout相似性参数
    parser.add_argument("--contrastive_level", default=1, type=int, help="对比损失的权重衰减速度")
    parser.add_argument("--use_dropout_sim", default=1, type=int, help="是否使用dropout相似性")

    # 批处理参数
    parser.add_argument("--batch_size", default=5, type=int, help="批处理大小")

    # 模型特定参数
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool, help="是否不使用包装的LM")
    parser.add_argument('--mean_verbalizer', default=True, type=bool, help="是否使用均值verbalizer")
    parser.add_argument("--lm_alpha", default=0.999, type=float, help="语言模型权重")

    # Few-shot参数
    parser.add_argument("--shot", type=int, default=8, help="few-shot样本数")
    parser.add_argument("--label_description", type=int, default=0, help="是否使用标签描述")

    # 随机种子和模型模式参数
    parser.add_argument("--seed", type=int, default=171, help="随机种子")
    parser.add_argument("--plm_eval_mode", default=False, help="预训练模型是否处于评估模式")
    parser.add_argument("--verbalizer", type=str, default="soft", help="verbalizer类型")

    # 模板参数
    parser.add_argument("--template_id", default=0, type=int, help="模板ID")

    # 手动参数
    parser.add_argument("--not_manual", default=False, type=int, help="是否非手动模式")
    parser.add_argument("--depth", default=9, type=int, help="层次深度")

    # 训练控制参数
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1, help="梯度累积步数")
    parser.add_argument("--max_epochs", type=int, default=20, help="最大训练轮数")

    # 早停参数
    parser.add_argument("--early_stop", default=10, type=int, help="早停轮数")

    # 完整评估参数
    parser.add_argument("--eval_full", default=0, type=int, help="是否进行完整评估")

    # 解析参数
    args = parser.parse_args()
    
    # 根据设备参数设置CUDA可见设备和设备对象
    if args.device != -1:
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
        device = torch.device("cuda:0")
        use_cuda = True
    else:
        use_cuda = False
        device = torch.device("cpu")

    # 如果未启用对比学习，则关闭相关功能
    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    # 转换shuffle参数为布尔值
    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False
    # 打印解析后的参数信息
    print_info(args)
    
    # 根据是否使用标签描述选择数据处理器
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)

    if args.label_description:
        # 使用带标签描述的数据处理器
        processor1 = PROCESSOR1[args.dataset](shot=args.shot, seed=args.seed)
        train_data = processor1.train_example
        dev_data = processor1.dev_example
        test_data = processor1.test_example
        # 构建数据集字典
        dataset = {}
        dataset['train'] = processor1.train_example
        dataset['dev'] = processor1.dev_example
        dataset['test'] = processor1.test_example
    else:
        # 使用标准数据处理器
        train_data = processor.train_example
        dev_data = processor.dev_example
        test_data = processor.test_example
        # 构建数据集字典
        dataset = {}
        dataset['train'] = processor.train_example
        dataset['dev'] = processor.dev_example
        dataset['test'] = processor.test_example

    # 将数据集转换为文本和标签的列表形式
    train_data = [[i.text_a, i.label] for i in train_data]
    dev_data = [[i.text_a, i.label] for i in dev_data]
    test_data = [[i.text_a, i.label] for i in test_data]
    
    # 获取层次映射关系
    hier_mapping = processor.hier_mapping
    # 根据层次映射更新深度参数
    args.depth = len(hier_mapping) + 1

    # 打印数据集大小信息
    print_info("final train_data length is: {}".format(len(train_data)))
    print_info("final dev_data length is: {}".format(len(dev_data)))
    print_info("final test_data length is: {}".format(len(test_data)))

    # 设置模板ID
    args.template_id = 0

    # 设置随机种子以保证实验可重现性
    set_seed(args.seed)

    # 加载预训练语言模型
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)


    # 设置最大序列长度和批处理大小
    max_seq_l = args.max_seq_lens
    batch_s = args.batch_size

    # 根据是否使用多mask选择模板文件
    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
    
    # 构建模板文件路径
    template_path = "template"
    
    # 构建模板文本
    text_mask = []
    for i in range(args.depth):
        text_mask.append(f'{i + 1} level: {"{mask}"}')
    text = f'It was {" ".join(text_mask)}. {"{placeholder": "text_a"}}'
    
    # 如果模板目录不存在则创建
    if not os.path.exists(template_path):
        os.mkdir(template_path)
    # 如果ckpts目录不存在则创建
    if not os.path.exists("ckpts"):
        os.mkdir("ckpts")
    
    # 构建完整模板路径
    template_path = os.path.join(template_path, template_file)
    
    # 如果模板文件不存在则创建
    if not os.path.exists(template_path):
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
    
    # 从文件加载模板
    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    # 打印训练数据大小
    print_info("train_size: {}".format(len(dataset['train'])))

    ## 数据加载部分
    # 创建训练数据加载器 注：decoder_max_length(最多生成token数量)、teacher_forcing、predict_eos_token 多用于生成任务，分类模型不需要
    train_dataloader = SinglePathPromptDataLoader(dataset=dataset['train'], template=mytemplate, tokenizer=tokenizer,
                                                  tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                  decoder_max_length=3,
                                                  batch_size=batch_s, shuffle=args.shuffle, teacher_forcing=False,
                                                  predict_eos_token=False, truncate_method="tail",
                                                  num_works=2,
                                                  multi_gpu=(args.device == -2), )
    # 根据数据集类型获取完整名称
    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        raise NotImplementedError

    # 构建验证和测试数据加载器的路径
    test_path = os.path.join(f"/data/TACL_math1_new/TACL2024/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
    dev_path = os.path.join(f"/data/TACL_math1_new/TACL2024/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")
    # 设置评估批处理大小
    eval_batch_s = 3
    
    # 加载或创建验证数据加载器
    if args.dataset != "dbp" and os.path.exists(dev_path):
        # 如果验证数据加载器文件存在，则直接加载
        validation_dataloader = torch.load(dev_path, weights_only=False)

    else:
        # 否则创建验证数据加载器  
        validation_dataloader = SinglePathPromptDataLoader(dataset=dataset["dev"], template=mytemplate,
                                                           tokenizer=tokenizer,
                                                           tokenizer_wrapper_class=WrapperClass,
                                                           max_seq_length=max_seq_l,
                                                           decoder_max_length=3,
                                                           batch_size=eval_batch_s, shuffle=False,
                                                           teacher_forcing=False,
                                                           predict_eos_token=False,
                                                           truncate_method="tail",
                                                           multi_gpu=False,
                                                           )
        # 如果不是DBpedia数据集，则保存验证数据加载器
        if args.dataset != "dbp":
            torch.save(validation_dataloader, dev_path)

    # 加载或创建测试数据加载器
    if not os.path.exists(test_path):
        # 如果测试数据加载器文件不存在，则创建并保存
        test_dataloader = SinglePathPromptDataLoader(dataset=dataset["test"], template=mytemplate, tokenizer=tokenizer,
                                                     tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                     decoder_max_length=3,
                                                     batch_size=eval_batch_s, shuffle=False, teacher_forcing=False,
                                                     predict_eos_token=False,
                                                     truncate_method="tail",
                                                     multi_gpu=False,
                                                     mode='test',
                                                     )
        torch.save(test_dataloader, test_path)
    else:
        # 否则直接加载测试数据加载器
        test_dataloader = torch.load(test_path, weights_only=False)

    ## 构建verbalizer和模型
    # 初始化verbalizer列表
    verbalizer_list = []
    label_list = processor.label_list

    # 为每个层级创建SoftVerbalizer
    for i in range(args.depth):
        if "0.1.2" in openprompt.__path__[0]:
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
        else:
            # verbalizer_list.append(SoftVerbalizer(tokenizer, plm=plm, classes=label_list[i]))
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    # 打印加载模型信息
    print_info("loading prompt model")
    # 创建层次化动词提示分类模型
    prompt_model = HierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,tokenizer=tokenizer,
                                              freeze_plm=args.freeze_plm, args=args, processor=processor,
                                              plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)

    # 将模型移动到指定设备
    if use_cuda:
        prompt_model = prompt_model.cuda()

    ## 准备训练参数
    # 不对biase和LayerNorm参数进行权重衰减
    no_decay = ['bias', 'LayerNorm.weight']

    # 获取模型参数
    named_parameters = prompt_model.plm.named_parameters()

    # 分组优化器参数1：用于PLM参数
    optimizer_grouped_parameters1 = [
        {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)],
         'weight_decay': 0.01},
        {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)],
         'weight_decay': 0.0}
    ]

    # 获取verbalizer
    verbalizer = prompt_model.verbalizer
    # 分组优化器参数2：用于verbalizer参数 对映射模型不同参数部分应用不同的优化策略
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]

    # 创建优化器
    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
    optimizer2 = AdamW(optimizer_grouped_parameters2)

    # 计算总训练步数
    tot_step = len(train_dataloader) // args.gradient_accumulation_steps * args.max_epochs

    # 初始化学习率调度器
    warmup_steps = 0
    scheduler1 = None
    scheduler2 = None
    if args.use_scheduler1:
        # 创建第一个学习率调度器
        scheduler1 = get_linear_schedule_with_warmup(
            optimizer1,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)
    if args.use_scheduler2:
        # 创建第二个学习率调度器
        scheduler2 = get_linear_schedule_with_warmup(
            optimizer2,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)

    # 对比学习权重
    contrastive_alpha = args.contrastive_alpha
    
    # 初始化最佳分数和早停计数器
    best_score_macro = 0  # 最佳macro F1分数
    best_score_micro = 0  # 最佳micro F1分数
    best_score_macro_epoch = -1  # 达到最佳macro F1的epoch
    best_score_micro_epoch = -1  # 达到最佳micro F1的epoch
    early_stop_count = 0  # 早停计数器

    # 如果不使用权重处理类别不平衡，则关闭反向处理
    if not args.imbalanced_weight:
        args.imbalanced_weight_reverse = False

    # 构建本次运行的唯一标识符，用于保存模型和结果
    this_run_unicode = f"{args.dataset}-seed{args.seed}-shot{args.shot}-lr{args.lr}-lr2{args.lr2}-batch_size{args.batch_size}-multi_mask{args.multi_mask}-use_new_ct{args.use_new_ct}-cs_mode{args.cs_mode}-ctl{args.contrastive_logits}" \
                       f"-contrastive_alpha{contrastive_alpha}-shuffle{args.shuffle}-constraint_loss{args.constraint_loss}-multi_verb{args.multi_verb}" \
                       f"-contrastive_level{args.contrastive_level}--use_dropout_sim{args.use_dropout_sim}-length{len(dataset['train'])}"
    print_info("saved_path: {}".format(this_run_unicode))

    # 如果进行完整评估，初始化最佳记录
    if args.eval_full:
        best_record = dict()
        keys = ['p_micro_f1', 'p_macro_f1', 'c_micro_f1', 'c_macro_f1', 'P_acc']
        for key in keys:
            best_record[key] = 0

    ## 开始训练过程
    for epoch in range(args.max_epochs):
        print_info("------------ epoch {} ------------".format(epoch + 1))
        
        # 检查是否触发早停机制
        if early_stop_count >= args.early_stop:
            print_info("Early stop!")
            break

        # 打印当前学习率
        print_info(
            f"cur lr\tscheduler1: {scheduler1.get_lr() if scheduler1 is not None else args.lr}\tscheduler2: {scheduler2.get_lr() if scheduler2 is not None else 1e-4}")

        # 初始化详细损失信息
        loss_detailed = [0, 0, 0, 0]
        prompt_model.train()
        idx = 0

        # 遍历训练数据批次
        for batch in tqdm(train_dataloader):
            # 将批次数据移至相应设备
            batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
            # 构建字典格式的批次数据
            batch = {"input_ids": batch[0], "attention_mask": batch[1],
                     "label": batch[2], "loss_ids": batch[3]}

            # 前向传播，计算logits、损失等
            logits, loss, cur_loss_detailed = prompt_model(batch)
            # 累加各类型损失
            loss_detailed = [loss_detailed[idx] + value for idx, value in enumerate(cur_loss_detailed)]
            # 反向传播计算梯度
            loss.backward()
            
            # 使用梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), args.max_grad_norm)

            # 更新模型参数
            optimizer1.step()
            optimizer2.step()

            # 更新学习率调度器
            if scheduler1 is not None:
                scheduler1.step()
            if scheduler2 is not None:
                scheduler2.step()

            # 清空优化器梯度
            optimizer1.zero_grad()
            optimizer2.zero_grad()

            idx = idx + 1
            # torch.cuda.empty_cache()
        
        # 打印详细损失信息
        print_info("multi-verb loss, lm loss, constraint loss, contrastive loss are: ")
        print_info(loss_detailed)

        # 在验证集上评估模型
        scores = prompt_model.evaluate(validation_dataloader, processor, desc="Valid",
                                       mode=args.eval_mode)
        early_stop_count += 1  # 增加早停计数器
        
        # 根据评估模式保存最佳模型
        if args.eval_full:
            score_str = ""
            for key in keys:
                score_str += f'{key} {scores[key]}\n'
            print_info(score_str)
            # 如果当前评估指标优于历史最佳，则更新记录并保存模型
            for k in best_record:
                if scores[k] > best_record[k]:
                    best_record[k] = scores[k]
                    torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-{k}.ckpt")
                    early_stop_count = 0

        else:
            # 获取macro和micro F1分数
            macro_f1 = scores['macro_f1']
            micro_f1 = scores['micro_f1']
            print_info('macro {} micro {}'.format(macro_f1, micro_f1))
            
            # 如果当前macro F1优于历史最佳
            if macro_f1 > best_score_macro:
                best_score_macro = macro_f1
                # 保存模型并重置早停计数器
                torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-macro.ckpt")
                early_stop_count = 0
                best_score_macro_epoch = epoch

            # 如果当前micro F1优于历史最佳
            if micro_f1 > best_score_micro:
                best_score_micro = micro_f1
                torch.save(prompt_model.state_dict(), f"ckpts/{this_run_unicode}-micro.ckpt")
                early_stop_count = 0
                best_score_micro_epoch = epoch

    ## 评估最终模型
    if args.eval_full:
        best_keys = ['P_acc']
        for k in best_keys:
            # 加载最佳模型
            prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-{k}.ckpt"))

            # 在测试集上评估模型
            scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode,
                                           args=args)
            tmp_str = ''
            tmp_str += f"finally best_{k} "
            for i in keys:
                tmp_str += f"{i}: {scores[i]}\t"
            print_info(tmp_str)

    else:
        # 加载最佳macro模型进行测试
        prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-macro.ckpt"))

        if use_cuda:
            prompt_model = prompt_model.cuda()

        # 在测试集上评估模型
        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode, args=args)
        macro_f1_1 = scores['macro_f1']
        micro_f1_1 = scores['micro_f1']
        acc_1 = scores['acc']
        print_info('finally best macro {} {} micro {} acc {}'.format(best_score_macro_epoch, macro_f1_1, micro_f1_1, acc_1))

        # 加载最佳micro模型进行测试
        prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-micro.ckpt"))

        # 在测试集上评估模型
        scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode, args=args)
        macro_f1_2 = scores['macro_f1']
        micro_f1_2 = scores['micro_f1']
        acc_2 = scores['acc']
        print_info('finally best micro {} {} micro {} acc {}'.format(best_score_micro_epoch, macro_f1_2, micro_f1_2, acc_2))

    ## 打印并记录参数详情
    content_write = "=" * 20 + "\n"
    content_write += f"start_time {start_time}" + "\n"
    content_write += f"end_time {datetime.datetime.now()}\t"
    # 记录所有超参数
    for hyperparam, value in args.__dict__.items():
        content_write += f"{hyperparam} {value}\t"
    content_write += "\n"
    
    # 如果进行完整评估，记录完整评估结果
    if args.eval_full:
        cur_keys = ['P_acc']
        for key in cur_keys:
            content_write += f"best_{key} "
            for i in keys:
                content_write += f"{i}: {best_record[i]}\t"
            content_write += f"\n"
    # 否则记录常规评估结果
    else:
        content_write += f"best_macro macro_f1: {macro_f1_1}\t"
        content_write += f"micro_f1: {micro_f1_1}\t"
        content_write += f"acc: {acc_1}\t\n"

        content_write += f"best_micro macro_f1: {macro_f1_2}\t"
        content_write += f"micro_f1: {micro_f1_2}\t"
        content_write += f"acc: {acc_2}\t"
    content_write += "\n\n"

    # 打印训练详情
    print_info(content_write)
    
    # 创建结果目录（如果不存在）
    if not os.path.exists("result"):
        os.mkdir("result")
    # 将评估结果写入文件
    with open(os.path.join("result", args.result_file), "a") as fout:
        fout.write(content_write)


if __name__ == "__main__":
    main()  # 如果是主程序，执行main函数