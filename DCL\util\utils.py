# -*- coding:utf-8 -*-
'''
工具函数模块
包含各种通用工具函数，如参数解析、模型加载、随机种子设置等
'''

# 导入所需的Python标准库和第三方库
import torch                         # PyTorch深度学习框架
import numpy as np                   # 数值计算库
from transformers import __version__ as transformers_version  # 获取transformers库版本
import random                        # 随机数生成模块
from transformers import BertTokenizer  # BERT分词器

# 从transformers库导入BERT相关组件
from transformers import BertConfig, BertForMaskedLM
from openprompt.plms.mlm import MLMTokenizerWrapper  # OpenPrompt的MLM包装器
import argparse                      # 命令行参数解析库

# 初始化全局日志记录器变量
logger = None


def print_info(info, file=None):
    '''
    通用信息打印函数，支持输出到日志或文件
    
    Args:
        info: 要打印的信息内容
        file: 可选的文件对象，用于输出到文件
    '''
    # 如果存在日志记录器，则使用日志记录器记录信息
    if logger is not None:
        logger.info(info)
    else:
        # 否则直接打印到控制台或指定文件
        print(info, file=file)


def parse_args(model="hierCRF"):
    '''
    解析命令行参数的函数
    
    Args:
        model: 模型类型，可选"hierVerb"或"hierCRF"
        
    Returns:
        args: 解析后的参数对象
    '''
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser("")

    # 基本模型参数设置
    parser.add_argument("--model", type=str, default=model, choices=['hierVerb', 'hierCRF'], help="模型类型")
    parser.add_argument("--model_name_or_path", default='/root/autodl-tmp/htc/bert-base-uncased', help="预训练模型路径")
    parser.add_argument("--result_file", type=str, default="few_shot_train.txt", help="结果保存文件")
    parser.add_argument("--multi_mask", type=int, default=1, help="是否使用多mask")
    parser.add_argument("--dropout", default=0.1, type=float, help="dropout率")
    parser.add_argument("--shuffle", default=0, type=int, help="是否打乱数据")

    # 训练控制参数
    parser.add_argument("--do_train", default=1, type=int, help="是否进行训练")
    parser.add_argument("--do_dev", default=1, type=bool, help="是否进行验证")
    parser.add_argument("--do_test", default=1, type=bool, help="是否进行测试")

    # 模型配置参数
    parser.add_argument("--not_manual", default=False, type=int, help="是否非手动模式")
    parser.add_argument("--depth", default=2, type=int, help="层次深度")

    # 设备参数
    parser.add_argument("--device", default=0, type=int, help="设备ID")

    # 训练参数
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1, help="梯度累积步数")

    # 数据集和评估参数
    parser.add_argument("--dataset", default="wos", type=str, help="数据集名称")
    parser.add_argument("--eval_mode", default=0, type=int, help="评估模式")
    parser.add_argument("--use_hier_mean", default=1, type=int, help="是否使用层次平均")
    parser.add_argument("--multi_verb", default=1, type=int, help="是否使用多动词")

    # 学习率调度器参数
    parser.add_argument("--use_scheduler1", default=1, type=int, help="是否使用第一个学习率调度器")
    parser.add_argument("--use_scheduler2", default=1, type=int, help="是否使用第二个学习率调度器")

    # 序列和模型参数
    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="最大梯度范数")
    parser.add_argument("--max_seq_lens", default=512, type=int, help="最大序列长度")
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool, help="是否不使用包装的LM")
    parser.add_argument('--mean_verbalizer', default=True, type=bool, help="是否使用平均verbalizer")

    # Few-shot参数
    parser.add_argument("--shot", type=int, default=1, help="few-shot样本数")
    parser.add_argument("--seed", type=int, default=171, help="随机种子")

    # 模型冻结和评估模式
    parser.add_argument("--freeze_plm", default=0, type=int, help="是否冻结预训练模型")
    parser.add_argument("--plm_eval_mode", default=False, help="预训练模型是否处于评估模式")
    parser.add_argument("--verbalizer", type=str, default="soft", help="verbalizer类型")

    # 模板参数
    parser.add_argument("--template_id", default=0, type=int, help="模板ID")

    # 多标签参数
    parser.add_argument("--multi_label", default=0, type=int, help="是否使用多标签")

    # 早停和完整评估参数
    parser.add_argument("--early_stop", default=10, type=int, help="早停轮数")
    parser.add_argument("--eval_full", default=0, type=int, help="是否进行完整评估")
    
    # 根据模型类型添加特定参数
    if model == "hierVerb":
        # hierVerb模型特有参数
        parser.add_argument("--use_new_ct", default=1, type=int, help="是否使用新的对比学习方法")
        parser.add_argument("--contrastive_loss", default=1, type=int, help="是否使用对比损失")
        parser.add_argument("--contrastive_level", default=1, type=int, help="对比学习层级")
        parser.add_argument("--contrastive_alpha", default=0.99, type=float, help="对比损失权重")
        parser.add_argument("--contrastive_logits", default=1, type=int, help="是否使用对比学习logits")
        parser.add_argument("--use_dropout_sim", default=1, type=int, help="是否使用dropout相似性")
        parser.add_argument("--imbalanced_weight", default=True, type=bool, help="是否使用权重处理类别不平衡")
        parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool, help="是否反向处理类别不平衡权重")
        parser.add_argument("--max_epochs", type=int, default=20, help="最大训练轮数")
        parser.add_argument("--constraint_loss", default=1, type=int, help="是否使用约束损失")
        parser.add_argument("--constraint_alpha", default=-1, type=float, help="约束损失权重")
        parser.add_argument("--cs_mode", default=0, type=int, help="对比学习模式")

        parser.add_argument("--lm_training", default=1, type=int, help="是否进行语言模型训练")
        parser.add_argument("--lm_alpha", default=0.999, type=float, help="语言模型权重")

        parser.add_argument("--lr", default=5e-5, type=float, help="主学习率")
        parser.add_argument("--lr2", default=1e-4, type=float, help="第二学习率")

        parser.add_argument("--batch_size", default=5, type=int, help="批处理大小")
        parser.add_argument("--eval_batch_size", default=20, type=int, help="评估批处理大小")

        # 解析并返回参数
        args = parser.parse_args()
        return args
    elif model == "hierCRF":
        # hierCRF模型特有参数
        parser.add_argument("--lr", default=5e-5, type=float, help="主学习率")
        parser.add_argument("--lr2", default=1e-4, type=float, help="第二学习率")
        parser.add_argument("--lr3", default=5e-2, type=float, help="第三学习率")
        parser.add_argument("--max_epochs", type=int, default=50, help="最大训练轮数")
        parser.add_argument("--hierCRF_loss", default=1, type=int, help="是否使用hierCRF损失")

        parser.add_argument("--hierCRF_alpha", default=-1, type=float, help="hierCRF损失权重")
        parser.add_argument("--batch_size", default=10, type=int, help="批处理大小")
        parser.add_argument("--eval_batch_size", default=20, type=int, help="评估批处理大小")

        parser.add_argument("--multi_verb_loss", default=1, type=int, help="是否使用多动词损失")
        parser.add_argument("--multi_verb_loss_alpha", default=-1, type=int, help="多动词损失权重")

        parser.add_argument("--lm_training", default=0, type=int, help="是否进行语言模型训练")
        parser.add_argument("--lm_alpha", default=0.999, type=float, help="语言模型权重")

        # 解析并返回参数
        args = parser.parse_args()
        return args
    else:
        # 如果模型类型不支持，则抛出异常
        raise NotImplementedError


def load_plm_from_config(args, model_path, specials_to_add=None, **kwargs):
    r"""A plm loader using a global config.
    It will load the model, tokenizer, and config simulatenously.

    Args:
        config (:obj:`CfgNode`): The global config from the CfgNode.

    Returns:
        :obj:`PreTrainedModel`: The pretrained model.
        :obj:`tokenizer`: The pretrained tokenizer.
        :obj:`model_config`: The config of the pretrained model.
        :obj:`wrapper`: The wrapper class of this plm.
    """
    # 从预训练路径加载模型配置
    model_config = BertConfig.from_pretrained(model_path)

    # 设置模型的dropout概率
    model_config.hidden_dropout_prob = args.dropout

    # 从预训练路径加载模型
    model = BertForMaskedLM.from_pretrained(model_path, config=model_config)
    # 加载分词器
    tokenizer = BertTokenizer.from_pretrained(model_path)
    
    # 设置包装器  作用如下：
    # 将经过模板加工后的文本（包含 {"mask"} 占位符）传入，
    # 使用底层 tokenizer 生成适合模型输入的 input_ids、attention_mask 等，
    # 标记哪些位置是“mask”作为需要预测的目标 （通过 loss_ids）
    # 构造符合 OpenPrompt 训练和推理格式的输入样本。

    wrapper = MLMTokenizerWrapper

    # 返回模型、分词器、配置和包装器
    return model, tokenizer, model_config, wrapper


def seed_torch(seed=1029):
    '''
    设置随机种子以确保实验可重现
    
    Args:
        seed: 隣机种子值
    '''
    # 打印设置的随机种子值
    print('Set seed to', seed)
    # 设置Python内置random模块的种子
    random.seed(seed)
    # 设置numpy的随机种子
    np.random.seed(seed)
    # 设置PyTorch的CPU随机种子
    torch.manual_seed(seed)
    # 设置PyTorch的GPU随机种子
    torch.cuda.manual_seed(seed)
    # 禁用cuDNN的benchmark模式以确保确定性
    torch.backends.cudnn.benchmark = False
    # 启用cuDNN的确定性模式
    torch.backends.cudnn.deterministic = True


def _mask_tokens(tokenizer, input_ids):
    """ 
    为掩码语言建模准备掩码token输入/标签: 80% MASK, 10% 随机, 10% 原始
    
    Args:
        tokenizer: 分词器
        input_ids: 输入token ID序列
        
    Returns:
        input_ids: 处理后的输入token ID序列
        labels: 对应的标签序列
    """
    # 克隆输入ID作为标签
    labels = input_ids.clone()
    # 为每个序列中的token创建概率矩阵，掩码概率为0.15
    probability_matrix = torch.full(labels.shape, 0.15)
    # 获取特殊token的掩码
    special_tokens_mask = [tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True) for val in
                           labels.tolist()]
    # 将特殊token位置的概率设为0，避免对特殊token进行掩码
    probability_matrix.masked_fill_(torch.tensor(special_tokens_mask, dtype=torch.bool), value=0.0)

    # 通过伯努利分布采样确定哪些token需要掩码
    masked_indices = torch.bernoulli(probability_matrix).bool()

    # 根据transformers版本确定忽略标记的值
    # 如果transformers版本大于等于2.4.0，使用-100作为忽略值
    if [int(v) for v in transformers_version.split('.')][:3] >= [2, 4, 0]:
        ignore_value = -100
    else:
        # 否则使用-1作为忽略值
        ignore_value = -1

    # 将非掩码位置的标签设为忽略值，只计算掩码token的损失
    labels[~masked_indices] = ignore_value

    # 80%的时间，我们用tokenizer.mask_token([MASK])替换掩码输入token
    indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
    input_ids[indices_replaced] = tokenizer.convert_tokens_to_ids(tokenizer.mask_token)

    # 10%的时间，我们用随机词替换掩码输入token
    indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
    random_words = torch.randint(len(tokenizer), labels.shape, dtype=torch.long)
    input_ids[indices_random] = random_words[indices_random]

    # 剩余时间(10%的时间)我们保持掩码输入token不变
    return input_ids, labels