# 对比学习中排除相同文本内容的解决方案

## 问题描述

在多标签对比学习场景中，当多个样本具有相同的文本内容但标签不同时，将它们作为负样本会产生冲突的训练信号。这是因为：

1. **相同文本内容**：在表示空间中应该具有相似的表示
2. **不同标签**：按照对比学习的逻辑，应该被推远
3. **冲突信号**：这种矛盾会影响模型训练效果

## 解决方案

### 核心思想

在负样本采样过程中，排除具有相同文本内容的样本，避免产生冲突的训练信号。

### 实现方式

#### 1. 函数签名修改

```python
def flat_contrastive_loss_func(label_sim, hier_labels, processor, output_at_mask, 
                              imbalanced_weight=False, depth=2,
                              contrastive_level=1,
                              imbalanced_weight_reverse=True, 
                              use_cuda=True, 
                              text_contents=None):  # 新增参数
```

#### 2. 核心逻辑

```python
# 检查文本内容是否相同
if text_contents is not None and text_contents[i] == text_contents[j] and i != j:
    cur_hier_matrix[i][j] = 0  # 设为0（忽略）
    continue
```

#### 3. 负样本采样改进

```python
# 确保不将相同文本内容的样本作为负样本
if text_contents is None or text_contents[i] != text_contents[l]:
    cur_hier_matrix[i][l] = 0
```

## 使用方法

### 1. 在模型中调用

```python
# 从batch中提取文本内容信息
text_contents = None
if 'input_ids' in batch:
    # 将input_ids转换为字符串用于比较
    text_contents = [str(ids.tolist()) for ids in batch['input_ids']]

# 调用对比学习损失函数
contrastive_loss = flat_contrastive_loss_func(
    label_sim=self.label_sim,
    hier_labels=hier_labels,
    processor=self.processor,
    output_at_mask=outputs_at_mask,
    imbalanced_weight=args.imbalanced_weight,
    contrastive_level=args.contrastive_level,
    imbalanced_weight_reverse=args.imbalanced_weight_reverse,
    depth=args.depth,
    use_cuda=self.use_cuda,
    text_contents=text_contents  # 传入文本内容
)
```

### 2. 更精确的文本内容获取

如果需要更精确的文本内容比较，可以在数据加载时保存原始文本：

```python
# 在数据加载器中添加原始文本
batch = {
    "input_ids": batch[0], 
    "attention_mask": batch[1],
    "label": batch[2], 
    "loss_ids": batch[3],
    "raw_texts": batch[4]  # 添加原始文本
}
```

## 代码修改位置

### 1. 损失函数 (`models/loss.py`)
- 添加 `text_contents` 参数
- 修改负样本采样逻辑
- 添加文本内容相同性检查

### 2. 模型文件
- `models/hierVerb.py`: 第179-192行
- `models/embedding_chy.py`: 第147-161行  
- `models/topk_chy.py`: 第148-162行

## 效果与优势

### 1. 避免冲突训练信号
- 相同文本内容的样本不会互相作为负样本
- 减少了训练过程中的矛盾信号

### 2. 提高训练质量
- 对比学习更加合理和一致
- 模型能够学习到更好的表示

### 3. 保持灵活性
- `text_contents=None` 时保持原有行为
- 向后兼容现有代码

## 注意事项

### 1. 性能考虑
- 文本内容比较会增加一定的计算开销
- 建议在训练前预处理文本内容标识

### 2. 内存使用
- 存储文本内容会增加内存使用
- 可以考虑使用文本哈希值代替完整文本

### 3. 文本预处理
- 确保文本内容的一致性（如空格、标点符号处理）
- 考虑是否需要标准化处理

## 示例代码

参见 `example_usage.py` 文件中的完整示例。

## 总结

这个解决方案通过在对比学习过程中排除相同文本内容的样本，有效避免了多标签场景下的冲突训练信号，提高了模型训练的质量和效果。实现简单且向后兼容，可以直接应用到现有的对比学习框架中。
