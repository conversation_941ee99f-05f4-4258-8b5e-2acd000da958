'''
Author: jike
Date: 2022-10-08 09:40:03
LastEditTime: 2022-11-21 15:57:29
LastEditors: jike
FilePath: /mnt/jike/paper/nlu/paper/train.py
Description: 文本嵌入生成脚本，用于生成文本的向量表示，支持层次文本分类任务

该脚本实现了基于Prompt的层次文本分类模型的训练和评估流程，主要功能包括：
1. 解析命令行参数，配置训练环境
2. 加载数据集和数据处理器
3. 构建Prompt模板和Verbalizer
4. 初始化模型和优化器
5. 执行模型评估并记录结果
'''

import datetime
import logging
from tqdm import tqdm
import os
import torch
import json
import argparse
import openprompt
from openprompt.utils.reproduciblity import set_seed
from openprompt.prompts import SoftVerbalizer, ManualTemplate

# 导入自定义的层次化动词提示分类模型
from models.embedding_chy import HierVerbPromptForClassification

# 导入数据处理器
from processor import PROCESSOR
from processor_des import PROCESSOR1

# 导入工具函数
from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader
from transformers import AdamW, get_linear_schedule_with_warmup


import logging

# 配置日志格式
logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)

use_cuda = True  # 是否使用CUDA的全局标志


def main():
    '''
    主函数，实现层次文本分类模型的训练和评估流程
    
    该函数的主要流程包括：
    1. 解析命令行参数
    2. 配置训练环境（设备、随机种子等）
    3. 加载数据集和数据处理器
    4. 构建Prompt模板和Verbalizer
    5. 初始化模型、优化器和学习率调度器
    6. 执行模型评估并记录结果
    '''
    start_time = datetime.datetime.now()  # 记录开始时间
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser("")

    # 模型相关参数
    parser.add_argument("--model", type=str, default='bert')  # 模型类型
    # parser.add_argument("--model_name_or_path", default='bert-base-uncased')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/backup/xmdas/models/models/google-bert/bert-base-chinese')  # 预训练模型路径
    parser.add_argument("--result_file", type=str, default="few_shot_train.txt")  # 结果文件名

    # 训练相关参数
    parser.add_argument("--multi_mask", type=int, default=1)  # 是否使用多mask

    parser.add_argument("--dropout", default=0.1, type=float)  # Dropout比率
    parser.add_argument("--shuffle", default=0, type=int)  # 是否打乱数据
    parser.add_argument("--contrastive_logits", default=1, type=int)  # 是否使用对比logits
    parser.add_argument("--constraint_loss", default=0, type=int)  # 是否使用约束损失
    parser.add_argument("--cs_mode", default=0, type=int)  # 约束模式
    parser.add_argument("--dataset", default="wos", type=str)  # 数据集名称
    parser.add_argument("--eval_mode", default=0, type=int)  # 评估模式
    parser.add_argument("--use_hier_mean", default=1, type=int)  # 是否使用层次平均
    parser.add_argument("--freeze_plm", default=0, type=int)  # 是否冻结预训练模型

    parser.add_argument("--multi_label", default=0, type=int)  # 是否多标签分类
    parser.add_argument("--multi_verb", default=1, type=int)  # 是否多动词

    parser.add_argument("--use_scheduler1", default=1, type=int)  # 是否使用调度器1
    parser.add_argument("--use_scheduler2", default=1, type=int)  # 是否使用调度器2

    parser.add_argument("--constraint_alpha", default=-1, type=float)  # 约束损失权重

    parser.add_argument("--imbalanced_weight", default=True, type=bool)  # 是否使用不平衡权重
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)  # 是否反转不平衡权重

    parser.add_argument("--device", default=1, type=int)  # 设备ID

    parser.add_argument("--lm_training", default=1, type=int)  # 是否进行语言模型训练
    parser.add_argument("--lr", default=5e-5, type=float)  # 学习率
    parser.add_argument("--lr2", default=1e-4, type=float)  # 第二学习率
    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")  # 最大梯度范数
    parser.add_argument("--max_seq_lens", default=512, type=int, help="Max sequence length.")  # 最大序列长度

    parser.add_argument("--use_new_ct", default=1, type=int)  # 是否使用新的对比学习
    parser.add_argument("--contrastive_loss", default=0, type=int)  # 是否使用对比损失
    parser.add_argument("--contrastive_alpha", default=0.99, type=float)  # 对比损失权重

    parser.add_argument("--contrastive_level", default=1, type=int)  # 对比学习层级
    parser.add_argument("--use_dropout_sim", default=1, type=int)  # 是否使用dropout相似性
    parser.add_argument("--batch_size", default=5, type=int)  # 批次大小

    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)  # 是否不使用包装的LM
    parser.add_argument('--mean_verbalizer', default=True, type=bool)  # 是否使用平均verbalizer
    parser.add_argument("--lm_alpha", default=0.999, type=float)  # 语言模型损失权重

    parser.add_argument("--shot", type=int, default=8)  # few-shot数量
    parser.add_argument("--label_description", type=int, default=0)  # 是否使用标签描述

    parser.add_argument("--seed", type=int, default=171)  # 随机种子
    parser.add_argument("--plm_eval_mode", default=False)  # 预训练模型评估模式
    parser.add_argument("--verbalizer", type=str, default="soft")  # verbalizer类型

    parser.add_argument("--template_id", default=0, type=int)  # 模板ID

    parser.add_argument("--not_manual", default=False, type=int)  # 是否非手动
    # parser.add_argument("--depth", default=2, type=int)
    parser.add_argument("--depth", default=9, type=int)  # 层次深度

    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)  # 梯度累积步数
    parser.add_argument("--max_epochs", type=int, default=20)  # 最大训练轮数

    parser.add_argument("--early_stop", default=10, type=int)  # 早停轮数

    parser.add_argument("--eval_full", default=0, type=int)  # 是否进行全面评估

    # 解析命令行参数
    args = parser.parse_args()
    
    # 配置设备
    if args.device != -1:
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"  # 设置可见GPU
        device = torch.device("cuda:0")  # 使用GPU
        use_cuda = True
    else:
        use_cuda = False
        device = torch.device("cpu")  # 使用CPU

    # 根据是否使用对比损失调整相关参数
    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    # 转换shuffle参数为布尔值
    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False
    print_info(args)  # 打印参数信息
    
    # 初始化数据处理器
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)

    # 根据是否使用标签描述选择不同的数据处理器
    if args.label_description:
        processor1 = PROCESSOR1[args.dataset](shot=args.shot, seed=args.seed)
        train_data = processor1.train_example
        dev_data = processor1.dev_example
        test_data = processor1.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor1.train_example
        dataset['dev'] = processor1.dev_example
        dataset['test'] = processor1.test_example
    else:
        train_data = processor.train_example
        dev_data = processor.dev_example
        test_data = processor.test_example
        # dataset
        dataset = {}
        dataset['train'] = processor.train_example
        dataset['dev'] = processor.dev_example
        dataset['test'] = processor.test_example
        
    # 提取文本和标签数据
    train_data = [[i.text_a, i.label] for i in train_data]
    dev_data = [[i.text_a, i.label] for i in dev_data]
    test_data = [[i.text_a, i.label] for i in test_data]
    
    # 获取层次映射关系并更新深度参数
    hier_mapping = processor.hier_mapping
    args.depth = len(hier_mapping) + 1

    # 打印数据集大小信息
    print_info("final train_data length is: {}".format(len(train_data)))
    print_info("final dev_data length is: {}".format(len(dev_data)))
    print_info("final test_data length is: {}".format(len(test_data)))

    args.template_id = 0  # 设置模板ID

    set_seed(args.seed)  # 设置随机种子以确保可重现性

    # 加载预训练语言模型及相关组件
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)

    # 获取序列长度和批次大小参数
    max_seq_l = args.max_seq_lens
    batch_s = args.batch_size

    # 根据是否使用多mask选择模板文件
    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
    template_path = "template"
    
    # 构建模板文本
    text_mask = []
    for i in range(args.depth):
        text_mask.append(f'{i + 1} level: {{"mask"}}')
    text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
    
    # 创建模板目录和检查点目录
    if not os.path.exists(template_path):
        os.mkdir(template_path)
    if not os.path.exists("ckpts"):
        os.mkdir("ckpts")
    template_path = os.path.join(template_path, template_file)
    
    # 如果模板文件不存在，则创建它
    if not os.path.exists(template_path):
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
            
    # 从文件加载模板
    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    print_info("train_size: {}".format(len(dataset['train'])))  # 打印训练集大小

    ## 加载训练数据
    train_dataloader = SinglePathPromptDataLoader(dataset=dataset['train'], template=mytemplate, tokenizer=tokenizer,
                                                  tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                  decoder_max_length=3,
                                                  batch_size=batch_s, shuffle=args.shuffle, teacher_forcing=False,
                                                  predict_eos_token=False, truncate_method="tail",
                                                  num_works=2,
                                                  multi_gpu=(args.device == -2), )
                                                  
    # 根据数据集名称设置完整名称
    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        raise NotImplementedError

    # test_path = os.path.join(f"dataset", full_name, f"test_dataloader-multi_mask.pt")
    # dev_path = os.path.join("dataset", full_name, f"dev_dataloader-multi_mask.pt")
    # 设置测试集和验证集数据路径
    test_path = os.path.join(f"/data/TACL_math1_new/TACL2024/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
    dev_path = os.path.join(f"/data/TACL_math1_new/TACL2024/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")
    # eval_batch_s = 20
    eval_batch_s = 1  # 评估批次大小
    
    # 如果验证集数据文件存在，则加载它，否则创建并保存
    if args.dataset != "dbp" and os.path.exists(dev_path):
        # validation_dataloader = torch.load(dev_path)
        validation_dataloader = torch.load(dev_path, weights_only=False)
    else:
        validation_dataloader = SinglePathPromptDataLoader(dataset=dataset["dev"], template=mytemplate,
                                                           tokenizer=tokenizer,
                                                           tokenizer_wrapper_class=WrapperClass,
                                                           max_seq_length=max_seq_l,
                                                           decoder_max_length=3,
                                                           batch_size=eval_batch_s, shuffle=False,
                                                           teacher_forcing=False,
                                                           predict_eos_token=False,
                                                           truncate_method="tail",
                                                           multi_gpu=False,
                                                           )
        if args.dataset != "dbp":
            torch.save(validation_dataloader, dev_path)
            
    # 如果测试集数据文件不存在，则创建并保存
    if not os.path.exists(test_path):
        test_dataloader = SinglePathPromptDataLoader(dataset=dataset["test"], template=mytemplate, tokenizer=tokenizer,
                                                     tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                     decoder_max_length=3,
                                                     batch_size=eval_batch_s, shuffle=False, teacher_forcing=False,
                                                     predict_eos_token=False,
                                                     truncate_method="tail",
                                                     multi_gpu=False,
                                                     mode='test',
                                                     )
        torch.save(test_dataloader, test_path)
    else:
        test_dataloader = SinglePathPromptDataLoader(dataset=dataset['test'], template=mytemplate, tokenizer=tokenizer,
                                                  tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
                                                  decoder_max_length=3,
                                                  batch_size=batch_s, shuffle=False, teacher_forcing=False,
                                                  predict_eos_token=False, truncate_method="tail",
                                                  num_works=2,
                                                  multi_gpu=(args.device == -2), )

    ## 构建verbalizer和模型
    verbalizer_list = []  # verbalizer列表
    label_list = processor.label_list  # 标签列表

    # 为每个层次创建SoftVerbalizer
    for i in range(args.depth):
        if "0.1.2" in openprompt.__path__[0]:
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
        else:
            # verbalizer_list.append(SoftVerbalizer(tokenizer, plm=plm, classes=label_list[i]))
            verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))

    print_info("loading prompt model")  # 打印模型加载信息
    # 初始化层次化动词提示分类模型
    prompt_model = HierVerbPromptForClassification(plm=plm, template=mytemplate, verbalizer_list=verbalizer_list,
                                              freeze_plm=args.freeze_plm, args=args, processor=processor,
                                              plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda)

    # 如果使用CUDA，则将模型移到GPU上
    if use_cuda:
        prompt_model = prompt_model.cuda()

    ## 准备训练参数
    # 设置不进行权重衰减的参数名称（偏置和LayerNorm参数）
    no_decay = ['bias', 'LayerNorm.weight']

    named_parameters = prompt_model.plm.named_parameters()  # 获取模型参数

    # 分组设置优化器参数（带权重衰减和不带权重衰减的参数）
    optimizer_grouped_parameters1 = [
        {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)],
         'weight_decay': 0.01},
        {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)],
         'weight_decay': 0.0}
    ]

    # 为verbalizer参数设置不同的优化器组
    # 使用1e-4的学习率加速verbalizer中层次标签词嵌入的收敛
    verbalizer = prompt_model.verbalizer
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]

    # 创建优化器
    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
    optimizer2 = AdamW(optimizer_grouped_parameters2)

    # 计算总训练步数
    tot_step = len(train_dataloader) // args.gradient_accumulation_steps * args.max_epochs

    warmup_steps = 0  # 预热步数
    scheduler1 = None
    scheduler2 = None
    
    # 根据参数决定是否使用学习率调度器
    if args.use_scheduler1:
        scheduler1 = get_linear_schedule_with_warmup(
            optimizer1,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)
    if args.use_scheduler2:
        scheduler2 = get_linear_schedule_with_warmup(
            optimizer2,
            num_warmup_steps=warmup_steps, num_training_steps=tot_step)

    # 初始化评估相关变量
    contrastive_alpha = args.contrastive_alpha
    best_score_macro = 0
    best_score_micro = 0
    best_score_macro_epoch = -1
    best_score_micro_epoch = -1
    early_stop_count = 0

    # 根据是否使用不平衡权重调整参数
    if not args.imbalanced_weight:
        args.imbalanced_weight_reverse = False
        
    # 构建本次运行的唯一标识符
    this_run_unicode = f"{args.dataset}-seed{args.seed}-shot{args.shot}-lr{args.lr}-lr2{args.lr2}-batch_size{args.batch_size}-multi_mask{args.multi_mask}-use_new_ct{args.use_new_ct}-cs_mode{args.cs_mode}-ctl{args.contrastive_logits}" \
                       f"-contrastive_alpha{contrastive_alpha}-shuffle{args.shuffle}-constraint_loss{args.constraint_loss}-multi_verb{args.multi_verb}" \
                       f"-contrastive_level{args.contrastive_level}--use_dropout_sim{args.use_dropout_sim}-length{len(dataset['train'])}"
    print_info("saved_path: {}".format(this_run_unicode))

    # 如果进行全面评估，初始化最佳记录
    if args.eval_full:
        best_record = dict()
        keys = ['p_micro_f1', 'p_macro_f1', 'c_micro_f1', 'c_macro_f1', 'P_acc']
        for key in keys:
            best_record[key] = 0

    ## 执行评估
    if args.eval_full:
        # 全面评估模式
        best_keys = ['P_acc']
        for k in best_keys:
            # 加载最佳模型权重
            prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-{k}.ckpt"))

            # 执行评估
            scores = prompt_model.evaluate(train_dataloader, processor, desc="test", mode=args.eval_mode,
                                           args=args)
            tmp_str = ''
            tmp_str += f"finally best_{k} "
            # 打印评估结果
            for i in keys:
                tmp_str += f"{i}: {scores[i]}\t"
            print_info(tmp_str)

    else:
        # 标准评估模式（基于macro F1）
        # 加载最佳macro模型权重
        prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-macro.ckpt"))

        # 如果使用CUDA，确保模型在GPU上
        if use_cuda:
            prompt_model = prompt_model.cuda()

        # 执行评估
        scores = prompt_model.evaluate(train_dataloader, processor, desc="test", mode=args.eval_mode,args=args)
        macro_f1_1 = scores['macro_f1']
        micro_f1_1 = scores['micro_f1']
        acc_1 = scores['acc']
        # 打印最佳macro评估结果
        print_info('finally best macro {} {} micro {} acc {}'.format(best_score_macro_epoch, macro_f1_1, micro_f1_1, acc_1))

        # # for best micro
        # prompt_model.load_state_dict(torch.load(f"ckpts/{this_run_unicode}-micro.ckpt"))

        # scores = prompt_model.evaluate(test_dataloader, processor, desc="test", mode=args.eval_mode)
        # macro_f1_2 = scores['macro_f1']
        # micro_f1_2 = scores['micro_f1']
        # acc_2 = scores['acc']
        # print_info('finally best micro {} {} micro {} acc {}'.format(best_score_micro_epoch, macro_f1_2, micro_f1_2, acc_2))

    ## 打印和记录参数详情
    content_write = "=" * 20 + "\n"
    content_write += f"start_time {start_time}" + "\n"
    content_write += f"end_time {datetime.datetime.now()}\t"
    # 记录所有超参数
    for hyperparam, value in args.__dict__.items():
        content_write += f"{hyperparam} {value}\t"
    content_write += "\n"
    
    # 根据评估模式记录结果
    if args.eval_full:
        cur_keys = ['P_acc']
        for key in cur_keys:
            content_write += f"best_{key} "
            for i in keys:
                content_write += f"{i}: {best_record[i]}\t"
            content_write += f"\n"
    else:
        content_write += f"best_macro macro_f1: {macro_f1_1}\t"
        content_write += f"micro_f1: {micro_f1_1}\t"
        content_write += f"acc: {acc_1}\t\n"

        # content_write += f"best_micro macro_f1: {macro_f1_2}\t"
        # content_write += f"micro_f1: {micro_f1_2}\t"
        # content_write += f"acc: {acc_2}\t"
    content_write += "\n\n"

    print_info(content_write)  # 打印结果
    
    # 创建结果目录并写入结果文件
    if not os.path.exists("result"):
        os.mkdir("result")
    with open(os.path.join("result", args.result_file), "a") as fout:
        fout.write(content_write)


if __name__ == "__main__":
    main()